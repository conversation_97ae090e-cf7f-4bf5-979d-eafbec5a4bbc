import torch
import time
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset

# 配置工作目录和设备
teacher_base_dir = "./train_process/teacher-student-dezh"
student_base_dir = "./train_process/teacher-student-dezh"
teacher_model_dir = Path(teacher_base_dir + "/checkpoints")
student_model_dir = Path(student_base_dir + "/checkpoints")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/de-zh.txt/dezh_cleaned.txt"

# 初始化数据集
dataset = DeZhTranslationDataset(data_dir)
max_seq_length = 48


def translate_with_teacher(src: str) -> tuple:
    """
    使用教师模型进行翻译
    :param src: 德语句子
    :return: (翻译结果, 推理时间)
    """
    # 加载教师模型
    teacher_model = torch.load(teacher_model_dir / 'teacher_best.pt', map_location=device, weights_only=False)
    teacher_model.to(device)
    teacher_model.eval()

    # 预处理输入句子
    src_tokens = [0] + dataset.de_vocab(dataset.de_tokenizer(src)) + [1]
    src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(device)

    # 初始化目标序列
    tgt_tensor = torch.tensor([[0]]).to(device)

    # 记录推理时间
    start_time = time.time()
    
    # 自回归生成翻译结果
    with torch.no_grad():
        for _ in range(max_seq_length):
            out, logits = teacher_model(src_tensor, tgt_tensor)
            predict = logits[:, -1]  # 取最后一个位置的预测
            next_token = torch.argmax(predict, dim=1)

            tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)

            if next_token.item() == 1:  # <eos>
                break

    end_time = time.time()
    inference_time = end_time - start_time

    # 将token索引转换为中文句子
    tgt_tokens = tgt_tensor.squeeze().tolist()
    translated = ' '.join(dataset.zh_vocab.lookup_tokens(tgt_tokens))

    return translated.replace("<s>", "").replace("</s>", "").strip(), inference_time


def translate_with_student(src: str) -> tuple:
    """
    使用学生模型进行翻译
    :param src: 德语句子
    :return: (翻译结果, 推理时间)
    """
    # 加载学生模型
    student_model = torch.load(student_model_dir / 'student_best.pt', map_location=device, weights_only=False)
    student_model.to(device)
    student_model.eval()

    # 预处理输入句子
    src_tokens = [0] + dataset.de_vocab(dataset.de_tokenizer(src)) + [1]
    src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(device)

    # 初始化目标序列
    tgt_tensor = torch.tensor([[0]]).to(device)

    # 记录推理时间
    start_time = time.time()
    
    # 自回归生成翻译结果
    with torch.no_grad():
        for _ in range(max_seq_length):
            out, logits = student_model(src_tensor, tgt_tensor)
            predict = logits[:, -1]  # 取最后一个位置的预测
            next_token = torch.argmax(predict, dim=1)

            tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)

            if next_token.item() == 1:  # <eos>
                break

    end_time = time.time()
    inference_time = end_time - start_time

    # 将token索引转换为中文句子
    tgt_tokens = tgt_tensor.squeeze().tolist()
    translated = ' '.join(dataset.zh_vocab.lookup_tokens(tgt_tokens))

    return translated.replace("<s>", "").replace("</s>", "").strip(), inference_time


def compare_models(src: str) -> dict:
    """
    比较教师模型和学生模型的翻译结果
    :param src: 德语句子
    :return: 比较结果字典
    """
    print(f"原文 (德语): {src}")
    print("-" * 60)
    
    # 教师模型翻译
    try:
        teacher_result, teacher_time = translate_with_teacher(src)
        print(f"教师模型翻译: {teacher_result}")
        print(f"教师模型推理时间: {teacher_time:.4f}秒")
    except Exception as e:
        teacher_result = f"错误: {str(e)}"
        teacher_time = 0
        print(f"教师模型翻译失败: {teacher_result}")
    
    print("-" * 60)
    
    # 学生模型翻译
    try:
        student_result, student_time = translate_with_student(src)
        print(f"学生模型翻译: {student_result}")
        print(f"学生模型推理时间: {student_time:.4f}秒")
    except Exception as e:
        student_result = f"错误: {str(e)}"
        student_time = 0
        print(f"学生模型翻译失败: {student_result}")
    
    print("-" * 60)
    
    # 性能比较
    if teacher_time > 0 and student_time > 0:
        speedup = teacher_time / student_time
        print(f"加速比: {speedup:.2f}x (学生模型比教师模型快 {speedup:.2f} 倍)")
    
    return {
        'source': src,
        'teacher_translation': teacher_result,
        'student_translation': student_result,
        'teacher_time': teacher_time,
        'student_time': student_time,
        'speedup': teacher_time / student_time if teacher_time > 0 and student_time > 0 else 0
    }


def get_model_info():
    """获取模型信息"""
    try:
        teacher_model = torch.load(teacher_model_dir / 'teacher_best.pt', map_location='cpu', weights_only=False)
        teacher_params = sum(p.numel() for p in teacher_model.parameters())
    except:
        teacher_params = 0
    
    try:
        student_model = torch.load(student_model_dir / 'student_best.pt', map_location='cpu', weights_only=False)
        student_params = sum(p.numel() for p in student_model.parameters())
    except:
        student_params = 0
    
    compression_ratio = teacher_params / student_params if student_params > 0 else 0
    
    print("=" * 60)
    print("模型信息")
    print("=" * 60)
    print(f"教师模型参数数量: {teacher_params:,}")
    print(f"学生模型参数数量: {student_params:,}")
    print(f"参数压缩比: {compression_ratio:.2f}x")
    print(f"模型大小减少: {(1 - 1/compression_ratio)*100:.1f}%")
    print("=" * 60)


# 测试翻译
if __name__ == '__main__':
    # 显示模型信息
    get_model_info()
    
    # 测试句子
    test_sentences = [
        "Am Anfang schuf Gott Himmel und Erde.",
        "Ich liebe dich.",
        "Guten Morgen.",
        "Wie geht es dir?",
        "Das Wetter ist heute schön."
    ]
    
    print("\n知识蒸馏模型翻译对比测试")
    print("=" * 60)
    
    for sentence in test_sentences:
        print(f"\n测试句子: {sentence}")
        result = compare_models(sentence)
        print("\n" + "=" * 60)
