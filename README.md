# 德语-中文翻译项目

这是一个基于Transformer模型的德语到中文翻译项目。

## 项目结构

```
├── data/
│   └── de-zh.txt/                    # 德语-中文数据集
│       ├── bible-uedin.de-zh.de      # 原始德语文件
│       ├── bible-uedin.de-zh.zh      # 原始中文文件
│       ├── dezh.txt                  # 合并后的训练数据
│       ├── dezh_cleaned.txt          # 清理后的训练数据
│       ├── vocab_de.pt               # 德语词汇表缓存
│       ├── vocab_zh.pt               # 中文词汇表缓存
│       ├── tokens_list_de.pt         # 德语token缓存
│       └── tokens_list_zh.pt         # 中文token缓存
├── dataset/
│   └── dezh.py                       # 德语-中文数据集类
├── model/
│   └── transformer.py               # Transformer模型定义
├── train_process/
│   ├── transformer-dezh/            # 基础训练过程目录
│   └── transformer-dezh-improved/   # 改进训练过程目录
├── prepare_dezh_data.py              # 数据预处理脚本
├── clean_dezh_data.py                # 数据清理脚本
├── train_dezh_transformer.py        # 基础训练脚本
├── train_dezh_improved.py           # 改进训练脚本
└── interface_dezh_transformer.py    # 翻译接口
```

## 数据集信息

- **原始数据**: 62,198 句对
- **清理后数据**: 53,483 句对 (保留率: 86.0%)
- **数据来源**: 圣经德语-中文对照文本
- **德语词汇表大小**: ~21,000 词
- **中文词汇表大小**: ~25,000 词

## 使用方法

### 1. 数据预处理

```bash
# 合并德语和中文文件
python prepare_dezh_data.py

# 清理数据（可选，提高质量）
python clean_dezh_data.py
```

### 2. 训练模型

```bash
# 基础训练
python train_dezh_transformer.py

# 改进训练（推荐）
python train_dezh_improved.py
```

### 3. 使用翻译接口

```python
from interface_dezh_transformer import translate

# 翻译德语句子
result = translate("Guten Morgen!")
print(result)  # 输出中文翻译
```

## 模型改进

### 已实现的改进：

1. **数据质量提升**
   - 数据清理和过滤
   - 移除过短或过长的句子
   - 验证德语词汇的存在

2. **训练优化**
   - 标签平滑损失函数
   - 学习率预热和衰减
   - 梯度裁剪
   - 更大的模型维度 (512)
   - 更长的序列长度 (48)

3. **训练策略**
   - 更小的批次大小 (64)
   - 更频繁的模型保存
   - 更多的训练轮次

## 训练参数

### 改进版训练参数：
- **模型维度**: 512
- **批次大小**: 64
- **最大序列长度**: 48
- **训练轮次**: 200
- **初始学习率**: 0.0001
- **预热步数**: 4000
- **标签平滑**: 0.1

## 注意事项

1. 训练需要GPU支持以获得合理的训练速度
2. 完整训练可能需要数小时到数天时间
3. 建议监控训练损失，及时停止过拟合
4. 可以根据需要调整训练参数

## 文件说明

- `prepare_dezh_data.py`: 将分离的德语和中文文件合并
- `clean_dezh_data.py`: 清理和过滤数据，提高质量
- `train_dezh_improved.py`: 包含多项改进的训练脚本
- `interface_dezh_transformer.py`: 提供简单的翻译接口
- `dataset/dezh.py`: 自定义数据集类，处理德语-中文数据

## 依赖项

- torch
- jieba (中文分词)
- zhconv (中文繁简转换)
- tqdm (进度条)
- tensorboard (训练监控)
