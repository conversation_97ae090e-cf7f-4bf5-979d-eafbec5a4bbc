import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel, StudentTransformerModel, DistillationLoss
from torch.utils.tensorboard import SummaryWriter
from torch.nn.functional import pad
from tqdm import tqdm
from pathlib import Path

# 工作目录配置
base_dir = "./train_process/distillation-dezh"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/checkpoints")

# 创建目录
work_dir.mkdir(parents=True, exist_ok=True)
model_dir.mkdir(parents=True, exist_ok=True)

log_dir = base_dir + "/logs"

# 训练参数配置
teacher_checkpoint = None  # 预训练的教师模型路径，如 'teacher_best.pt'
student_checkpoint = None  # 学生模型检查点，如 'student_5000.pt'
batch_size = 32  # 蒸馏训练通常使用较小的batch size
epochs = 100
save_after_step = 1000
max_seq_length = 48
data_dir = "data/de-zh.txt/dezh_cleaned.txt"
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

# 蒸馏参数
alpha = 0.7  # 蒸馏损失权重
temperature = 4.0  # 温度参数
initial_lr = 0.0001


def collate_fn(batch):
    """数据批处理函数"""
    bs_id = 0  # <bos> index
    eos_id = 1  # <eos> index
    pad_id = 2  # <pad> index

    src_list, tgt_list = [], []

    for _src, _tgt in batch:
        src_tensor = torch.tensor(_src[:max_seq_length - 2], dtype=torch.int64)
        tgt_tensor = torch.tensor(_tgt[:max_seq_length - 2], dtype=torch.int64)

        processed_src = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            src_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        processed_tgt = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            tgt_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        src_list.append(pad(
            processed_src,
            (0, max_seq_length - len(processed_src)),
            value=pad_id
        ))

        tgt_list.append(pad(
            processed_tgt,
            (0, max_seq_length - len(processed_tgt)),
            value=pad_id
        ))

    src = torch.stack(src_list).to(device)
    tgt = torch.stack(tgt_list).to(device)
    tgt_y = tgt[:, 1:]
    tgt = tgt[:, :-1]

    return src, tgt, tgt_y


# 数据集加载
print("正在加载数据集...")
dataset = DeZhTranslationDataset(data_dir)
train_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)

print(f"数据集大小: {len(dataset)}")
print(f"德语词汇表大小: {len(dataset.de_vocab)}")
print(f"中文词汇表大小: {len(dataset.zh_vocab)}")

# 创建教师模型
if teacher_checkpoint and os.path.exists(model_dir / teacher_checkpoint):
    print(f"加载预训练教师模型: {teacher_checkpoint}")
    teacher_model = torch.load(model_dir / teacher_checkpoint, weights_only=False)
else:
    print("创建新的教师模型...")
    teacher_model = TeacherTransformerModel(
        d_model=512,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=max_seq_length,
        device=device,
        num_layers=6,
        num_heads=8
    )

teacher_model = teacher_model.to(device)
teacher_model.eval()  # 教师模型设为评估模式

# 创建学生模型
if student_checkpoint and os.path.exists(model_dir / student_checkpoint):
    print(f"加载学生模型检查点: {student_checkpoint}")
    student_model = torch.load(model_dir / student_checkpoint, weights_only=False)
else:
    print("创建新的学生模型...")
    student_model = StudentTransformerModel(
        d_model=256,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=max_seq_length,
        device=device,
        num_layers=3,
        num_heads=4
    )

student_model = student_model.to(device)

print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")
print(f"学生模型参数数量: {sum(p.numel() for p in student_model.parameters()):,}")
print(f"参数压缩比: {sum(p.numel() for p in teacher_model.parameters()) / sum(p.numel() for p in student_model.parameters()):.2f}x")

# 损失函数和优化器
criterion = DistillationLoss(alpha=alpha, temperature=temperature)
optimizer = torch.optim.AdamW(student_model.parameters(), lr=initial_lr, weight_decay=0.01)
writer = SummaryWriter(log_dir)


def train():
    """知识蒸馏训练函数"""
    step = 0
    best_loss = float('inf')

    for epoch in range(epochs):
        student_model.train()
        epoch_total_loss = 0
        epoch_hard_loss = 0
        epoch_soft_loss = 0
        
        loop = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()

            # 教师模型前向传播（不计算梯度）
            with torch.no_grad():
                teacher_out, teacher_logits = teacher_model(src, tgt)

            # 学生模型前向传播
            student_out, student_logits = student_model(src, tgt)

            # 计算蒸馏损失
            total_loss, hard_loss, soft_loss = criterion(
                student_logits.contiguous().view(-1, student_logits.size(-1)),
                teacher_logits.contiguous().view(-1, teacher_logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            # 反向传播
            total_loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
            
            optimizer.step()

            # 记录损失
            epoch_total_loss += total_loss.item()
            epoch_hard_loss += hard_loss.item()
            epoch_soft_loss += soft_loss.item()

            # 记录到tensorboard
            writer.add_scalar('Loss/Total', total_loss.item(), step)
            writer.add_scalar('Loss/Hard', hard_loss.item(), step)
            writer.add_scalar('Loss/Soft', soft_loss.item(), step)

            loop.set_postfix({
                'total_loss': total_loss.item(),
                'hard_loss': hard_loss.item(),
                'soft_loss': soft_loss.item()
            })

            step += 1

            # 定期保存模型
            if step != 0 and step % save_after_step == 0:
                torch.save(student_model, model_dir / f"student_{step}.pt")
                avg_loss = epoch_total_loss / (batch_idx + 1)
                if avg_loss < best_loss:
                    torch.save(student_model, model_dir / 'student_best.pt')
                    best_loss = avg_loss
                    print(f"\n新的最佳学生模型已保存，损失: {best_loss:.4f}")

        # 每个epoch结束后的统计
        avg_total_loss = epoch_total_loss / len(train_loader)
        avg_hard_loss = epoch_hard_loss / len(train_loader)
        avg_soft_loss = epoch_soft_loss / len(train_loader)
        
        print(f"Epoch {epoch+1} - 总损失: {avg_total_loss:.4f}, 硬损失: {avg_hard_loss:.4f}, 软损失: {avg_soft_loss:.4f}")
        
        # 保存最佳模型
        if avg_total_loss < best_loss:
            torch.save(student_model, model_dir / 'student_best.pt')
            best_loss = avg_total_loss
            print(f"新的最佳学生模型已保存，损失: {best_loss:.4f}")

    print("知识蒸馏训练完成！")


if __name__ == "__main__":
    print("开始知识蒸馏训练...")
    print(f"蒸馏参数 - Alpha: {alpha}, Temperature: {temperature}")
    train()
