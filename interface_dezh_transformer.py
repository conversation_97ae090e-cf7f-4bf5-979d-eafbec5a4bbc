import torch
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset

# 配置工作目录和设备
base_dir = "./train_process/transformer-dezh-improved"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/transformer_checkpoints")
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
data_dir = "data/de-zh.txt/dezh_cleaned.txt"

# 初始化数据集
dataset = DeZhTranslationDataset(data_dir)
max_seq_length = 42  # 句子最大长度


def translate(src: str) -> str:
    """
    使用训练好的Transformer模型进行德中翻译
    :param src: 德语句子，如"Ich liebe maschinelles Lernen."
    :return: 翻译后的中文句子，如"我喜欢机器学习"
    """
    # 加载模型
    model = torch.load(model_dir / 'best.pt', map_location=device)
    model.to(device)
    model.eval()

    # 预处理输入句子
    src_tokens = [dataset.de_vocab['<s>']] + [dataset.de_vocab[token] for token in dataset.de_tokenizer(src)] + [dataset.de_vocab['</s>']]  # 添加<bos>和<eos>
    src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(device)  # shape: [1, seq_len]

    # 初始化目标序列（以<bos>开头）
    tgt_tensor = torch.tensor([[dataset.zh_vocab['<s>']]]).to(device)  # shape: [1, 1]

    # 自回归生成翻译结果
    with torch.no_grad():
        for _ in range(max_seq_length):
            out = model(src_tensor, tgt_tensor)  # transformer计算
            predict = model.predictor(out[:, -1])  # 只取最后一个词的预测
            next_token = torch.argmax(predict, dim=1)  # 选择概率最高的词

            # 将新词添加到目标序列
            tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)

            # 遇到<eos>（索引1）则停止生成
            if next_token.item() == dataset.zh_vocab['</s>']:
                break

    # 将token索引转换为中文句子
    tgt_tokens = tgt_tensor.squeeze().tolist()
    translated = ' '.join(dataset.zh_vocab.lookup_tokens(tgt_tokens))

    # 清理特殊标记并返回结果
    return translated.replace("<s>", "").replace("</s>", "").strip()


# 测试翻译
if __name__ == '__main__':
    test_sentences = [
        "Am Anfang schuf Gott Himmel und Erde.",
        "Und das Wort war bei Gott, und das Wort war Gott.",
        "Fürchte dich nicht, denn ich bin mit dir.",
        "Unser Vater im Himmel! Dein Name werde geheiligt.",
        "Dein Reich komme. Dein Wille geschehe, wie im Himmel, so auf Erden.",
        "Ich liebe dich.",
        "Guten Morgen.",
        "Wer ist mein Nächster?",
        "Du sollst nicht töten.",
        "Liebet eure Feinde."
    ]

    print("====== 德语 → 中文 翻译测试 ======\n")
    for sentence in test_sentences:
        result = translate(sentence)
        print(f"[DE] {sentence}\n[ZH] {result}\n")
