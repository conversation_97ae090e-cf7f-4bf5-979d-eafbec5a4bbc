"""
模型诊断脚本
深入分析模型问题并提供解决方案
"""
import torch
import torch.nn.functional as F
import numpy as np
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset


class ModelDiagnostic:
    def __init__(self, teacher_path, student_path, data_path, device='auto'):
        if device == 'auto':
            self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"使用设备: {self.device}")
        
        # 加载数据集
        self.dataset = DeZhTranslationDataset(data_path)
        
        # 加载模型
        self.teacher_model = None
        self.student_model = None
        
        if Path(teacher_path).exists():
            self.teacher_model = torch.load(teacher_path, map_location=self.device, weights_only=False)
            self.teacher_model.to(self.device)
            self.teacher_model.eval()
        
        if Path(student_path).exists():
            self.student_model = torch.load(student_path, map_location=self.device, weights_only=False)
            self.student_model.to(self.device)
            self.student_model.eval()
    
    def check_model_structure(self):
        """检查模型结构"""
        print("\n" + "="*60)
        print("模型结构诊断")
        print("="*60)
        
        if self.teacher_model:
            print("\n教师模型结构:")
            print(f"模型类型: {type(self.teacher_model).__name__}")
            print(f"是否有predictor: {hasattr(self.teacher_model, 'predictor')}")
            
            # 检查模型的forward方法
            import inspect
            forward_sig = inspect.signature(self.teacher_model.forward)
            print(f"forward方法签名: {forward_sig}")
        
        if self.student_model:
            print("\n学生模型结构:")
            print(f"模型类型: {type(self.student_model).__name__}")
            print(f"是否有predictor: {hasattr(self.student_model, 'predictor')}")
            
            # 检查模型的forward方法
            import inspect
            forward_sig = inspect.signature(self.student_model.forward)
            print(f"forward方法签名: {forward_sig}")
    
    def test_model_output(self):
        """测试模型输出"""
        print("\n" + "="*60)
        print("模型输出诊断")
        print("="*60)
        
        # 创建测试输入
        test_src = torch.tensor([[0, 100, 200, 1]]).to(self.device)  # 简单测试序列
        test_tgt = torch.tensor([[0, 50]]).to(self.device)
        
        print(f"测试输入 - src: {test_src.shape}, tgt: {test_tgt.shape}")
        
        if self.teacher_model:
            print("\n教师模型输出:")
            try:
                with torch.no_grad():
                    output = self.teacher_model(test_src, test_tgt)
                    if isinstance(output, tuple):
                        out, logits = output
                        print(f"输出类型: tuple")
                        print(f"out shape: {out.shape}")
                        print(f"logits shape: {logits.shape}")
                        print(f"logits范围: [{logits.min().item():.3f}, {logits.max().item():.3f}]")
                        
                        # 检查概率分布
                        probs = F.softmax(logits[0, -1], dim=-1)
                        top_probs, top_indices = torch.topk(probs, 5)
                        print(f"最高概率tokens: {top_indices.tolist()}")
                        print(f"对应概率: {top_probs.tolist()}")
                    else:
                        print(f"输出类型: tensor")
                        print(f"output shape: {output.shape}")
                        
                        if hasattr(self.teacher_model, 'predictor'):
                            logits = self.teacher_model.predictor(output)
                            print(f"predictor输出shape: {logits.shape}")
            except Exception as e:
                print(f"教师模型测试失败: {e}")
        
        if self.student_model:
            print("\n学生模型输出:")
            try:
                with torch.no_grad():
                    output = self.student_model(test_src, test_tgt)
                    if isinstance(output, tuple):
                        out, logits = output
                        print(f"输出类型: tuple")
                        print(f"out shape: {out.shape}")
                        print(f"logits shape: {logits.shape}")
                        print(f"logits范围: [{logits.min().item():.3f}, {logits.max().item():.3f}]")
                        
                        # 检查概率分布
                        probs = F.softmax(logits[0, -1], dim=-1)
                        top_probs, top_indices = torch.topk(probs, 5)
                        print(f"最高概率tokens: {top_indices.tolist()}")
                        print(f"对应概率: {top_probs.tolist()}")
                        
                        # 检查是否有重复预测
                        predicted_tokens = torch.argmax(logits[0], dim=-1)
                        unique_tokens = torch.unique(predicted_tokens)
                        print(f"预测的唯一token数: {len(unique_tokens)}")
                        if len(unique_tokens) < len(predicted_tokens) * 0.5:
                            print("⚠ 警告: 预测输出重复率过高")
                    else:
                        print(f"输出类型: tensor")
                        print(f"output shape: {output.shape}")
            except Exception as e:
                print(f"学生模型测试失败: {e}")
    
    def analyze_vocabulary(self):
        """分析词汇表"""
        print("\n" + "="*60)
        print("词汇表分析")
        print("="*60)
        
        print(f"德语词汇表大小: {len(self.dataset.de_vocab)}")
        print(f"中文词汇表大小: {len(self.dataset.zh_vocab)}")
        
        # 检查特殊token
        special_tokens = ["<s>", "</s>", "<pad>", "<unk>"]
        print("\n特殊token索引:")
        for token in special_tokens:
            de_idx = self.dataset.de_vocab[token] if token in self.dataset.de_vocab.stoi else "未找到"
            zh_idx = self.dataset.zh_vocab[token] if token in self.dataset.zh_vocab.stoi else "未找到"
            print(f"{token}: DE={de_idx}, ZH={zh_idx}")
        
        # 检查一些常见词汇
        print("\n常见德语词汇索引:")
        common_de_words = ["der", "die", "das", "und", "ist", "ich", "du", "er", "sie", "es"]
        for word in common_de_words:
            idx = self.dataset.de_vocab[word] if word in self.dataset.de_vocab.stoi else "未找到"
            print(f"{word}: {idx}")
    
    def test_translation_step_by_step(self, text="Hallo"):
        """逐步测试翻译过程"""
        print("\n" + "="*60)
        print(f"逐步翻译诊断: '{text}'")
        print("="*60)
        
        # 分词
        de_tokens = self.dataset.de_tokenizer(text)
        print(f"德语分词: {de_tokens}")
        
        # 转换为索引
        token_indices = self.dataset.de_vocab[de_tokens]
        print(f"token索引: {token_indices}")
        
        # 构建输入
        src_tokens = [0] + token_indices + [1]
        src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
        print(f"源序列: {src_tokens}")
        
        # 逐步生成
        if self.student_model:
            print("\n学生模型逐步生成:")
            tgt_tensor = torch.tensor([[0]]).to(self.device)
            
            for step in range(10):  # 只生成前10步
                with torch.no_grad():
                    output = self.student_model(src_tensor, tgt_tensor)
                    if isinstance(output, tuple):
                        out, logits = output
                        predict = logits[:, -1]
                    else:
                        out = output
                        predict = self.student_model.predictor(out[:, -1])
                    
                    # 获取概率分布
                    probs = F.softmax(predict, dim=-1)
                    top_probs, top_indices = torch.topk(probs, 3)
                    
                    next_token = torch.argmax(predict, dim=1)
                    next_word = self.dataset.zh_vocab.lookup_token(next_token.item())
                    
                    print(f"步骤 {step+1}: token={next_token.item()}, 词='{next_word}'")
                    print(f"  前3候选: {[(self.dataset.zh_vocab.lookup_token(idx.item()), prob.item()) for idx, prob in zip(top_indices[0], top_probs[0])]}")
                    
                    tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)
                    
                    if next_token.item() == 1:  # <eos>
                        print("遇到结束符，停止生成")
                        break
            
            # 最终结果
            final_tokens = tgt_tensor.squeeze().tolist()
            final_words = self.dataset.zh_vocab.lookup_tokens(final_tokens)
            print(f"\n最终序列: {final_tokens}")
            print(f"最终翻译: {' '.join(final_words)}")
    
    def suggest_improvements(self):
        """提供改进建议"""
        print("\n" + "="*60)
        print("改进建议")
        print("="*60)
        
        print("基于诊断结果，建议以下改进措施:")
        print()
        print("1. 模型训练问题:")
        print("   - 教师模型可能训练不充分，建议增加训练轮次")
        print("   - 学生模型出现重复输出，可能是蒸馏参数不当")
        print("   - 建议调整温度参数和蒸馏权重")
        print()
        print("2. 推理问题:")
        print("   - 考虑使用beam search而不是贪婪搜索")
        print("   - 添加重复惩罚机制")
        print("   - 调整生成长度限制")
        print()
        print("3. 数据问题:")
        print("   - 检查训练数据质量")
        print("   - 考虑增加数据清理步骤")
        print("   - 平衡数据集中的句子长度分布")
        print()
        print("4. 模型架构:")
        print("   - 考虑调整学生模型的大小")
        print("   - 尝试不同的注意力机制")
        print("   - 添加正则化技术")


def main():
    teacher_path = "train_process/distillation/checkpoints/teacher_best.pt"
    student_path = "train_process/distillation/checkpoints/student_best.pt"
    data_path = "data/de-zh.txt/dezh_cleaned.txt"
    
    diagnostic = ModelDiagnostic(teacher_path, student_path, data_path)
    
    # 运行所有诊断
    diagnostic.check_model_structure()
    diagnostic.test_model_output()
    diagnostic.analyze_vocabulary()
    diagnostic.test_translation_step_by_step("Guten Morgen")
    diagnostic.suggest_improvements()


if __name__ == "__main__":
    main()
