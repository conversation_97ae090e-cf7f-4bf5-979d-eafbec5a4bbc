import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from dataset.dezh import DeZhTranslationDataset
from model.distillation_transformer import TeacherTransformerModel, StudentTransformerModel, DistillationLoss
from torch.utils.tensorboard import SummaryWriter
from torch.nn.functional import pad, log_softmax
from tqdm import tqdm
from pathlib import Path

# 工作目录配置
base_dir = "./train_process/teacher-student-dezh"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/checkpoints")

# 创建目录
work_dir.mkdir(parents=True, exist_ok=True)
model_dir.mkdir(parents=True, exist_ok=True)

log_dir = base_dir + "/logs"

# 训练参数配置
batch_size = 32
teacher_epochs = 50  # 教师模型训练轮次
student_epochs = 100  # 学生模型蒸馏训练轮次
save_after_step = 1000
max_seq_length = 48
data_dir = "data/de-zh.txt/dezh_cleaned.txt"
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

# 蒸馏参数
alpha = 0.7
temperature = 4.0
initial_lr = 0.0001


class LabelSmoothingLoss(nn.Module):
    """标签平滑损失函数"""
    def __init__(self, smoothing=0.1):
        super(LabelSmoothingLoss, self).__init__()
        self.smoothing = smoothing
        self.padding_idx = 2

    def forward(self, x, target):
        x = log_softmax(x, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(x)
            true_dist.fill_(self.smoothing / (x.size(-1) - 1))
            true_dist.scatter_(1, target.data.unsqueeze(1), 1.0 - self.smoothing)
            mask = (target == self.padding_idx).nonzero(as_tuple=False)
            if mask.numel() > 0:
                true_dist[mask.squeeze(), :] = 0
        return torch.sum(-true_dist * x, dim=-1).mean()


def collate_fn(batch):
    """数据批处理函数"""
    bs_id = 0  # <bos> index
    eos_id = 1  # <eos> index
    pad_id = 2  # <pad> index

    src_list, tgt_list = [], []

    for _src, _tgt in batch:
        src_tensor = torch.tensor(_src[:max_seq_length - 2], dtype=torch.int64)
        tgt_tensor = torch.tensor(_tgt[:max_seq_length - 2], dtype=torch.int64)

        processed_src = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            src_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        processed_tgt = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            tgt_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        src_list.append(pad(
            processed_src,
            (0, max_seq_length - len(processed_src)),
            value=pad_id
        ))

        tgt_list.append(pad(
            processed_tgt,
            (0, max_seq_length - len(processed_tgt)),
            value=pad_id
        ))

    src = torch.stack(src_list).to(device)
    tgt = torch.stack(tgt_list).to(device)
    tgt_y = tgt[:, 1:]
    tgt = tgt[:, :-1]

    return src, tgt, tgt_y


def train_teacher_model():
    """第一阶段：训练教师模型"""
    print("=" * 50)
    print("第一阶段：训练教师模型")
    print("=" * 50)
    
    # 数据集加载
    dataset = DeZhTranslationDataset(data_dir)
    train_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)

    # 创建教师模型
    teacher_model = TeacherTransformerModel(
        d_model=512,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=max_seq_length,
        device=device,
        num_layers=6,
        num_heads=8
    )
    teacher_model = teacher_model.to(device)

    print(f"教师模型参数数量: {sum(p.numel() for p in teacher_model.parameters()):,}")

    # 损失函数和优化器
    criterion = LabelSmoothingLoss(smoothing=0.1)
    optimizer = torch.optim.AdamW(teacher_model.parameters(), lr=initial_lr, weight_decay=0.01)
    writer = SummaryWriter(log_dir + "/teacher")

    step = 0
    best_loss = float('inf')

    for epoch in range(teacher_epochs):
        teacher_model.train()
        epoch_loss = 0
        loop = tqdm(train_loader, desc=f"Teacher Epoch {epoch+1}/{teacher_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()

            out, logits = teacher_model(src, tgt)
            loss = criterion(
                logits.contiguous().view(-1, logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            loss.backward()
            torch.nn.utils.clip_grad_norm_(teacher_model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_loss += loss.item()
            writer.add_scalar('Loss/Teacher', loss.item(), step)
            loop.set_postfix(loss=loss.item())

            step += 1

            if step != 0 and step % save_after_step == 0:
                torch.save(teacher_model, model_dir / f"teacher_{step}.pt")

        avg_loss = epoch_loss / len(train_loader)
        print(f"Teacher Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
        
        if avg_loss < best_loss:
            torch.save(teacher_model, model_dir / 'teacher_best.pt')
            best_loss = avg_loss
            print(f"新的最佳教师模型已保存，损失: {best_loss:.4f}")

    writer.close()
    print("教师模型训练完成！")
    return teacher_model


def train_student_model(teacher_model):
    """第二阶段：知识蒸馏训练学生模型"""
    print("=" * 50)
    print("第二阶段：知识蒸馏训练学生模型")
    print("=" * 50)
    
    # 数据集加载
    dataset = DeZhTranslationDataset(data_dir)
    train_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)

    # 创建学生模型
    student_model = StudentTransformerModel(
        d_model=256,
        src_vocab=dataset.de_vocab,
        tgt_vocab=dataset.zh_vocab,
        max_seq_length=max_seq_length,
        device=device,
        num_layers=3,
        num_heads=4
    )
    student_model = student_model.to(device)

    print(f"学生模型参数数量: {sum(p.numel() for p in student_model.parameters()):,}")
    print(f"参数压缩比: {sum(p.numel() for p in teacher_model.parameters()) / sum(p.numel() for p in student_model.parameters()):.2f}x")

    # 教师模型设为评估模式
    teacher_model.eval()

    # 损失函数和优化器
    criterion = DistillationLoss(alpha=alpha, temperature=temperature)
    optimizer = torch.optim.AdamW(student_model.parameters(), lr=initial_lr, weight_decay=0.01)
    writer = SummaryWriter(log_dir + "/student")

    step = 0
    best_loss = float('inf')

    for epoch in range(student_epochs):
        student_model.train()
        epoch_total_loss = 0
        epoch_hard_loss = 0
        epoch_soft_loss = 0
        
        loop = tqdm(train_loader, desc=f"Student Epoch {epoch+1}/{student_epochs}")
        
        for batch_idx, (src, tgt, tgt_y) in enumerate(loop):
            optimizer.zero_grad()

            # 教师模型前向传播（不计算梯度）
            with torch.no_grad():
                teacher_out, teacher_logits = teacher_model(src, tgt)

            # 学生模型前向传播
            student_out, student_logits = student_model(src, tgt)

            # 计算蒸馏损失
            total_loss, hard_loss, soft_loss = criterion(
                student_logits.contiguous().view(-1, student_logits.size(-1)),
                teacher_logits.contiguous().view(-1, teacher_logits.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            total_loss.backward()
            torch.nn.utils.clip_grad_norm_(student_model.parameters(), max_norm=1.0)
            optimizer.step()

            epoch_total_loss += total_loss.item()
            epoch_hard_loss += hard_loss.item()
            epoch_soft_loss += soft_loss.item()

            writer.add_scalar('Loss/Total', total_loss.item(), step)
            writer.add_scalar('Loss/Hard', hard_loss.item(), step)
            writer.add_scalar('Loss/Soft', soft_loss.item(), step)

            loop.set_postfix({
                'total': total_loss.item(),
                'hard': hard_loss.item(),
                'soft': soft_loss.item()
            })

            step += 1

            if step != 0 and step % save_after_step == 0:
                torch.save(student_model, model_dir / f"student_{step}.pt")

        avg_total_loss = epoch_total_loss / len(train_loader)
        avg_hard_loss = epoch_hard_loss / len(train_loader)
        avg_soft_loss = epoch_soft_loss / len(train_loader)
        
        print(f"Student Epoch {epoch+1} - 总损失: {avg_total_loss:.4f}, 硬损失: {avg_hard_loss:.4f}, 软损失: {avg_soft_loss:.4f}")
        
        if avg_total_loss < best_loss:
            torch.save(student_model, model_dir / 'student_best.pt')
            best_loss = avg_total_loss
            print(f"新的最佳学生模型已保存，损失: {best_loss:.4f}")

    writer.close()
    print("学生模型蒸馏训练完成！")
    return student_model


def main():
    """主训练流程"""
    print("开始两阶段训练：教师模型 -> 知识蒸馏")
    print(f"设备: {device}")
    print(f"蒸馏参数 - Alpha: {alpha}, Temperature: {temperature}")
    
    # 第一阶段：训练教师模型
    teacher_model = train_teacher_model()
    
    # 第二阶段：知识蒸馏训练学生模型
    student_model = train_student_model(teacher_model)
    
    print("=" * 50)
    print("两阶段训练完成！")
    print("=" * 50)
    print(f"教师模型保存在: {model_dir}/teacher_best.pt")
    print(f"学生模型保存在: {model_dir}/student_best.pt")


if __name__ == "__main__":
    main()
