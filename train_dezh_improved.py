import os
import torch
import torch.nn as nn
from torch.utils.data import DataLoader
from dataset.dezh import DeZhTranslationDataset
from torch.utils.tensorboard import SummaryWriter
from torch.nn.functional import pad, log_softmax
from tqdm import tqdm
from pathlib import Path
from model.transformer import TranslationModel
import random

# 工作目录配置
base_dir = "./train_process/transformer-dezh-improved"
work_dir = Path(base_dir)
model_dir = Path(base_dir + "/transformer_checkpoints")

# 创建目录
work_dir.mkdir(parents=True, exist_ok=True)
model_dir.mkdir(parents=True, exist_ok=True)

log_dir = base_dir + "/logs"

# 改进的训练参数配置
model_checkpoint = None  # 'model_10000.pt'
batch_size = 64 # 进一步减小batch size
epochs = 100  # 更多训练轮次
save_after_step = 1000  # 更频繁保存
max_seq_length = 24  # 更长的序列长度
data_dir = "data/de-zh.txt/dezh.txt"
device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')

# 学习率调度
initial_lr = 0.0001
warmup_steps = 4000


def get_lr(step):
    """学习率调度函数"""
    if step < warmup_steps:
        return initial_lr * (step / warmup_steps)
    else:
        return initial_lr * (warmup_steps / step) ** 0.5


# 数据加载和批处理函数
def collate_fn(batch):
    bs_id = 0  # <bos> index
    eos_id = 1  # <eos> index
    pad_id = 2  # <pad> index

    src_list, tgt_list = [], []

    for _src, _tgt in batch:
        # 限制序列长度，为特殊token留出空间
        src_tensor = torch.tensor(_src[:max_seq_length - 2], dtype=torch.int64)
        tgt_tensor = torch.tensor(_tgt[:max_seq_length - 2], dtype=torch.int64)

        processed_src = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            src_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        processed_tgt = torch.cat([
            torch.tensor([bs_id], dtype=torch.int64),
            tgt_tensor,
            torch.tensor([eos_id], dtype=torch.int64)
        ])

        src_list.append(pad(
            processed_src,
            (0, max_seq_length - len(processed_src)),
            value=pad_id
        ))

        tgt_list.append(pad(
            processed_tgt,
            (0, max_seq_length - len(processed_tgt)),
            value=pad_id
        ))

    src = torch.stack(src_list).to(device)
    tgt = torch.stack(tgt_list).to(device)
    tgt_y = tgt[:, 1:]
    tgt = tgt[:, :-1]
    n_tokens = (tgt_y != pad_id).sum()

    return src, tgt, tgt_y, n_tokens


# 数据集加载
print("正在加载数据集...")
dataset = DeZhTranslationDataset(data_dir)
train_loader = DataLoader(dataset, batch_size=batch_size, shuffle=True, collate_fn=collate_fn)

# 模型加载
if model_checkpoint:
    model = torch.load(model_dir / model_checkpoint, weights_only=False)
else:
    # 使用更大的模型维度
    model = TranslationModel(512, dataset.de_vocab, dataset.zh_vocab, max_seq_length, device)
model = model.to(device)

print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
print(f"德语词汇表大小: {len(dataset.de_vocab)}")
print(f"中文词汇表大小: {len(dataset.zh_vocab)}")


# 改进的损失函数
class LabelSmoothingLoss(nn.Module):
    def __init__(self, smoothing=0.1):
        super(LabelSmoothingLoss, self).__init__()
        self.smoothing = smoothing
        self.padding_idx = 2

    def forward(self, x, target):
        x = log_softmax(x, dim=-1)
        with torch.no_grad():
            true_dist = torch.zeros_like(x)
            true_dist.fill_(self.smoothing / (x.size(-1) - 1))
            true_dist.scatter_(1, target.data.unsqueeze(1), 1.0 - self.smoothing)
            mask = (target == self.padding_idx).nonzero(as_tuple=False)
            if mask.numel() > 0:
                true_dist[mask.squeeze(), :] = 0
        return torch.sum(-true_dist * x, dim=-1).mean()


criterion = LabelSmoothingLoss(smoothing=0.1)
optimizer = torch.optim.AdamW(model.parameters(), lr=initial_lr, weight_decay=0.01)
writer = SummaryWriter(log_dir)


# 训练函数
def train():
    step = 0
    best_loss = float('inf')

    for epoch in range(epochs):
        model.train()
        epoch_loss = 0
        loop = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
        
        for batch_idx, (src, tgt, tgt_y, n_tokens) in enumerate(loop):
            # 动态学习率调整
            lr = get_lr(step + 1)
            for param_group in optimizer.param_groups:
                param_group['lr'] = lr

            optimizer.zero_grad()

            out = model(src, tgt)
            out = model.predictor(out)

            # 计算损失
            loss = criterion(
                out.contiguous().view(-1, out.size(-1)),
                tgt_y.contiguous().view(-1)
            )

            # 反向传播和优化
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()

            # 记录日志
            writer.add_scalar('loss', loss.item(), step)
            writer.add_scalar('learning_rate', lr, step)
            
            epoch_loss += loss.item()
            loop.set_postfix(loss=loss.item(), lr=lr)

            step += 1

            # 定期保存模型
            if step != 0 and step % save_after_step == 0:
                torch.save(model, model_dir / f"model_{step}.pt")
                avg_loss = epoch_loss / (batch_idx + 1)
                if avg_loss < best_loss:
                    torch.save(model, model_dir / 'best.pt')
                    best_loss = avg_loss
                    print(f"\n新的最佳模型已保存，损失: {best_loss:.4f}")

        # 每个epoch结束后保存
        avg_epoch_loss = epoch_loss / len(train_loader)
        print(f"Epoch {epoch+1} 平均损失: {avg_epoch_loss:.4f}")
        
        if avg_epoch_loss < best_loss:
            torch.save(model, model_dir / 'best.pt')
            best_loss = avg_epoch_loss
            print(f"新的最佳模型已保存，损失: {best_loss:.4f}")


if __name__ == "__main__":
    print("开始训练德语-中文翻译模型...")
    train()
