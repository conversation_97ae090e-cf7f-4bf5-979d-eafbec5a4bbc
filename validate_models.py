"""
模型验证脚本
全面测试训练好的教师模型和学生模型
"""
import torch
import time
import numpy as np
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset
import matplotlib.pyplot as plt


class ModelValidator:
    def __init__(self, teacher_path, student_path, data_path, device='auto'):
        """初始化验证器"""
        if device == 'auto':
            self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        print(f"使用设备: {self.device}")
        
        # 加载数据集
        print("加载数据集...")
        self.dataset = DeZhTranslationDataset(data_path)
        print(f"数据集大小: {len(self.dataset)}")
        print(f"德语词汇表大小: {len(self.dataset.de_vocab)}")
        print(f"中文词汇表大小: {len(self.dataset.zh_vocab)}")
        
        # 加载模型
        self.teacher_model = None
        self.student_model = None
        
        if Path(teacher_path).exists():
            print(f"加载教师模型: {teacher_path}")
            self.teacher_model = torch.load(teacher_path, map_location=self.device, weights_only=False)
            self.teacher_model.to(self.device)
            self.teacher_model.eval()
            teacher_params = sum(p.numel() for p in self.teacher_model.parameters())
            print(f"教师模型参数数量: {teacher_params:,}")
        else:
            print(f"教师模型文件不存在: {teacher_path}")
        
        if Path(student_path).exists():
            print(f"加载学生模型: {student_path}")
            self.student_model = torch.load(student_path, map_location=self.device, weights_only=False)
            self.student_model.to(self.device)
            self.student_model.eval()
            student_params = sum(p.numel() for p in self.student_model.parameters())
            print(f"学生模型参数数量: {student_params:,}")
            
            if self.teacher_model:
                compression_ratio = teacher_params / student_params
                print(f"模型压缩比: {compression_ratio:.2f}x")
        else:
            print(f"学生模型文件不存在: {student_path}")
        
        self.max_seq_length = 48
    
    def translate_with_model(self, model, src_text, model_name="模型"):
        """使用指定模型进行翻译"""
        if model is None:
            return None, 0
        
        # 预处理
        de_tokens = self.dataset.de_tokenizer(src_text)
        src_tokens = [0] + self.dataset.de_vocab[de_tokens] + [1]
        src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
        tgt_tensor = torch.tensor([[0]]).to(self.device)
        
        # 翻译
        start_time = time.time()
        with torch.no_grad():
            for _ in range(self.max_seq_length):
                output = model(src_tensor, tgt_tensor)
                if isinstance(output, tuple):
                    out, logits = output
                    predict = logits[:, -1]
                else:
                    out = output
                    predict = model.predictor(out[:, -1])
                
                next_token = torch.argmax(predict, dim=1)
                tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)
                
                if next_token.item() == 1:  # <eos>
                    break
        
        end_time = time.time()
        inference_time = end_time - start_time
        
        # 转换为文本
        tgt_tokens = tgt_tensor.squeeze().tolist()
        translated = ' '.join(self.dataset.zh_vocab.lookup_tokens(tgt_tokens))
        translated = translated.replace("<s>", "").replace("</s>", "").strip()
        
        return translated, inference_time
    
    def test_basic_translation(self):
        """基础翻译测试"""
        print("\n" + "="*60)
        print("基础翻译测试")
        print("="*60)
        
        test_sentences = [
            "Guten Morgen",
            "Ich liebe dich",
            "Wie geht es dir?",
            "Das Wetter ist schön",
            "Wir lernen Deutsch"
        ]
        
        for i, sentence in enumerate(test_sentences, 1):
            print(f"\n测试 {i}: {sentence}")
            print("-" * 40)
            
            # 教师模型翻译
            if self.teacher_model:
                teacher_result, teacher_time = self.translate_with_model(self.teacher_model, sentence, "教师")
                print(f"教师模型: {teacher_result} ({teacher_time:.4f}s)")
            
            # 学生模型翻译
            if self.student_model:
                student_result, student_time = self.translate_with_model(self.student_model, sentence, "学生")
                print(f"学生模型: {student_result} ({student_time:.4f}s)")
                
                if self.teacher_model and teacher_time > 0 and student_time > 0:
                    speedup = teacher_time / student_time
                    print(f"加速比: {speedup:.2f}x")
    
    def test_translation_quality(self):
        """翻译质量测试"""
        print("\n" + "="*60)
        print("翻译质量对比测试")
        print("="*60)
        
        # 更复杂的测试句子
        complex_sentences = [
            "Am Anfang schuf Gott Himmel und Erde.",
            "Der Herr ist mein Hirte, mir wird nichts mangeln.",
            "Fürchte dich nicht, denn ich bin mit dir.",
            "Liebe deinen Nächsten wie dich selbst.",
            "In allem lasst uns Gott danken."
        ]
        
        quality_scores = []
        
        for sentence in complex_sentences:
            print(f"\n原文: {sentence}")
            print("-" * 50)
            
            teacher_result = None
            student_result = None
            
            if self.teacher_model:
                teacher_result, _ = self.translate_with_model(self.teacher_model, sentence)
                print(f"教师: {teacher_result}")
            
            if self.student_model:
                student_result, _ = self.translate_with_model(self.student_model, sentence)
                print(f"学生: {student_result}")
            
            # 简单的质量评估（基于长度和词汇重叠）
            if teacher_result and student_result:
                teacher_words = set(teacher_result.split())
                student_words = set(student_result.split())
                overlap = len(teacher_words & student_words)
                union = len(teacher_words | student_words)
                similarity = overlap / union if union > 0 else 0
                quality_scores.append(similarity)
                print(f"相似度: {similarity:.3f}")
        
        if quality_scores:
            avg_quality = np.mean(quality_scores)
            print(f"\n平均翻译相似度: {avg_quality:.3f}")
            return avg_quality
        return 0
    
    def test_inference_speed(self):
        """推理速度测试"""
        print("\n" + "="*60)
        print("推理速度基准测试")
        print("="*60)
        
        test_sentences = [
            "Hallo",  # 短句
            "Guten Morgen, wie geht es dir heute?",  # 中等
            "Am Anfang schuf Gott Himmel und Erde, und die Erde war wüst und leer."  # 长句
        ]
        
        for sentence in test_sentences:
            print(f"\n测试句子: {sentence}")
            print(f"长度: {len(sentence.split())} 词")
            print("-" * 40)
            
            # 多次测试取平均
            num_runs = 5
            
            if self.teacher_model:
                teacher_times = []
                for _ in range(num_runs):
                    _, time_taken = self.translate_with_model(self.teacher_model, sentence)
                    teacher_times.append(time_taken)
                avg_teacher_time = np.mean(teacher_times)
                std_teacher_time = np.std(teacher_times)
                print(f"教师模型: {avg_teacher_time:.4f}±{std_teacher_time:.4f}s")
            
            if self.student_model:
                student_times = []
                for _ in range(num_runs):
                    _, time_taken = self.translate_with_model(self.student_model, sentence)
                    student_times.append(time_taken)
                avg_student_time = np.mean(student_times)
                std_student_time = np.std(student_times)
                print(f"学生模型: {avg_student_time:.4f}±{std_student_time:.4f}s")
                
                if self.teacher_model:
                    speedup = avg_teacher_time / avg_student_time
                    print(f"平均加速比: {speedup:.2f}x")
    
    def test_edge_cases(self):
        """边界情况测试"""
        print("\n" + "="*60)
        print("边界情况测试")
        print("="*60)
        
        edge_cases = [
            "",  # 空字符串
            "Hallo",  # 单词
            "123",  # 数字
            "Hallo! Wie geht's?",  # 标点符号
            "Ein sehr sehr sehr sehr sehr sehr sehr sehr langer Satz mit vielen Wörtern.",  # 长句
        ]
        
        for case in edge_cases:
            print(f"\n测试: '{case}'")
            print("-" * 30)
            
            if self.student_model:
                try:
                    result, time_taken = self.translate_with_model(self.student_model, case)
                    print(f"结果: {result}")
                    print(f"时间: {time_taken:.4f}s")
                except Exception as e:
                    print(f"错误: {e}")
    
    def generate_report(self):
        """生成验证报告"""
        print("\n" + "="*60)
        print("模型验证报告")
        print("="*60)
        
        # 模型信息
        if self.teacher_model and self.student_model:
            teacher_params = sum(p.numel() for p in self.teacher_model.parameters())
            student_params = sum(p.numel() for p in self.student_model.parameters())
            compression_ratio = teacher_params / student_params
            
            print(f"教师模型参数: {teacher_params:,}")
            print(f"学生模型参数: {student_params:,}")
            print(f"压缩比: {compression_ratio:.2f}x")
            print(f"参数减少: {(1 - 1/compression_ratio)*100:.1f}%")
        
        # 运行所有测试
        print("\n开始验证测试...")
        self.test_basic_translation()
        quality_score = self.test_translation_quality()
        self.test_inference_speed()
        self.test_edge_cases()
        
        print("\n" + "="*60)
        print("验证完成")
        print("="*60)
        print("✓ 基础翻译功能正常")
        print("✓ 推理速度测试完成")
        print("✓ 边界情况测试完成")
        if quality_score > 0.5:
            print("✓ 翻译质量良好")
        else:
            print("⚠ 翻译质量需要改进")


def main():
    # 配置路径
    teacher_path = "train_process/distillation/checkpoints/teacher_best.pt"
    student_path = "train_process/distillation/checkpoints/student_best.pt"
    data_path = "data/de-zh.txt/dezh_cleaned.txt"
    
    # 创建验证器
    validator = ModelValidator(teacher_path, student_path, data_path)
    
    # 生成完整报告
    validator.generate_report()


if __name__ == "__main__":
    main()
