"""
德语-中文翻译接口
支持教师模型和学生模型的翻译及性能比较
"""
import torch
import time
import argparse
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset


class Translator:
    def __init__(self, model_path, data_path, device='auto'):
        """
        初始化翻译器
        :param model_path: 模型文件路径
        :param data_path: 数据集路径（用于加载词汇表）
        :param device: 设备
        """
        if device == 'auto':
            self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 加载数据集（主要为了词汇表）
        self.dataset = DeZhTranslationDataset(data_path)
        
        # 加载模型
        self.model = torch.load(model_path, map_location=self.device, weights_only=False)
        self.model.to(self.device)
        self.model.eval()
        
        self.max_seq_length = 48
        
        print(f"模型加载成功: {model_path}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"设备: {self.device}")
    
    def translate(self, src_text: str, verbose=False) -> tuple:
        """
        翻译德语文本到中文
        :param src_text: 德语输入文本
        :param verbose: 是否显示详细信息
        :return: (翻译结果, 推理时间)
        """
        if verbose:
            print(f"原文: {src_text}")
        
        # 预处理输入句子
        src_tokens = [0] + self.dataset.de_vocab(self.dataset.de_tokenizer(src_text)) + [1]
        src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
        
        # 初始化目标序列
        tgt_tensor = torch.tensor([[0]]).to(self.device)
        
        # 记录推理时间
        start_time = time.time()
        
        # 自回归生成翻译结果
        with torch.no_grad():
            for _ in range(self.max_seq_length):
                # 检查模型输出格式
                output = self.model(src_tensor, tgt_tensor)
                if isinstance(output, tuple):
                    # 蒸馏模型返回 (out, logits)
                    out, logits = output
                    predict = logits[:, -1]
                else:
                    # 原始模型只返回 out
                    out = output
                    predict = self.model.predictor(out[:, -1])
                
                next_token = torch.argmax(predict, dim=1)
                tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)
                
                if next_token.item() == 1:  # <eos>
                    break
        
        end_time = time.time()
        inference_time = end_time - start_time
        
        # 将token索引转换为中文句子
        tgt_tokens = tgt_tensor.squeeze().tolist()
        translated = ' '.join(self.dataset.zh_vocab.lookup_tokens(tgt_tokens))
        translated = translated.replace("<s>", "").replace("</s>", "").strip()
        
        if verbose:
            print(f"译文: {translated}")
            print(f"推理时间: {inference_time:.4f}秒")
        
        return translated, inference_time


def compare_models(teacher_path, student_path, data_path, test_sentences):
    """比较教师模型和学生模型的性能"""
    print("=" * 80)
    print("教师模型 vs 学生模型性能比较")
    print("=" * 80)
    
    # 创建翻译器
    teacher_translator = Translator(teacher_path, data_path)
    student_translator = Translator(student_path, data_path)
    
    print(f"\n教师模型参数: {sum(p.numel() for p in teacher_translator.model.parameters()):,}")
    print(f"学生模型参数: {sum(p.numel() for p in student_translator.model.parameters()):,}")
    compression_ratio = sum(p.numel() for p in teacher_translator.model.parameters()) / sum(p.numel() for p in student_translator.model.parameters())
    print(f"压缩比: {compression_ratio:.2f}x")
    
    total_teacher_time = 0
    total_student_time = 0
    
    for i, sentence in enumerate(test_sentences, 1):
        print(f"\n测试 {i}: {sentence}")
        print("-" * 60)
        
        # 教师模型翻译
        teacher_result, teacher_time = teacher_translator.translate(sentence)
        print(f"教师模型: {teacher_result} ({teacher_time:.4f}s)")
        
        # 学生模型翻译
        student_result, student_time = student_translator.translate(sentence)
        print(f"学生模型: {student_result} ({student_time:.4f}s)")
        
        # 速度比较
        speedup = teacher_time / student_time if student_time > 0 else 0
        print(f"加速比: {speedup:.2f}x")
        
        total_teacher_time += teacher_time
        total_student_time += student_time
    
    # 总体性能统计
    avg_speedup = total_teacher_time / total_student_time if total_student_time > 0 else 0
    print("\n" + "=" * 80)
    print("总体性能统计")
    print("=" * 80)
    print(f"平均教师模型推理时间: {total_teacher_time/len(test_sentences):.4f}s")
    print(f"平均学生模型推理时间: {total_student_time/len(test_sentences):.4f}s")
    print(f"平均加速比: {avg_speedup:.2f}x")
    print(f"模型大小减少: {(1 - 1/compression_ratio)*100:.1f}%")


def main():
    parser = argparse.ArgumentParser(description='德语-中文翻译')
    parser.add_argument('--mode', choices=['translate', 'compare'], default='translate', help='运行模式')
    parser.add_argument('--teacher_model', default='./train_process/distillation/checkpoints/teacher_best.pt', help='教师模型路径')
    parser.add_argument('--student_model', default='./train_process/distillation/checkpoints/student_best.pt', help='学生模型路径')
    parser.add_argument('--data_path', default='data/de-zh.txt/dezh_cleaned.txt', help='数据集路径')
    parser.add_argument('--text', help='要翻译的德语文本')
    parser.add_argument('--device', default='auto', help='设备')
    
    args = parser.parse_args()
    
    if args.mode == 'translate':
        if not args.text:
            # 交互式翻译
            print("德语-中文翻译器 (输入 'quit' 退出)")
            print("加载学生模型...")
            translator = Translator(args.student_model, args.data_path, args.device)
            
            while True:
                text = input("\n请输入德语文本: ").strip()
                if text.lower() in ['quit', 'exit', 'q']:
                    break
                if text:
                    result, time_taken = translator.translate(text, verbose=True)
        else:
            # 单次翻译
            translator = Translator(args.student_model, args.data_path, args.device)
            result, time_taken = translator.translate(args.text, verbose=True)
    
    elif args.mode == 'compare':
        # 性能比较模式
        test_sentences = [
            "Am Anfang schuf Gott Himmel und Erde.",
            "Ich liebe dich.",
            "Guten Morgen.",
            "Wie geht es dir?",
            "Das Wetter ist heute schön.",
            "Wir lernen Deutsch.",
            "Die Katze sitzt auf dem Stuhl.",
            "Ich gehe zur Schule."
        ]
        
        if Path(args.teacher_model).exists() and Path(args.student_model).exists():
            compare_models(args.teacher_model, args.student_model, args.data_path, test_sentences)
        else:
            print("错误: 找不到模型文件")
            print(f"教师模型: {args.teacher_model}")
            print(f"学生模型: {args.student_model}")


if __name__ == "__main__":
    main()
