"""
改进的德语-中文翻译接口
实现beam search和重复惩罚机制
"""
import torch
import torch.nn.functional as F
import time
import argparse
from pathlib import Path
from dataset.dezh import DeZhTranslationDataset
import numpy as np


class BeamSearchTranslator:
    def __init__(self, model_path, data_path, device='auto'):
        """初始化翻译器"""
        if device == 'auto':
            self.device = torch.device('cuda:0' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
        
        # 加载数据集
        self.dataset = DeZhTranslationDataset(data_path)
        
        # 加载模型
        self.model = torch.load(model_path, map_location=self.device, weights_only=False)
        self.model.to(self.device)
        self.model.eval()
        
        self.max_seq_length = 48
        self.bos_token = 0
        self.eos_token = 1
        self.pad_token = 2
        self.unk_token = 3
        
        print(f"模型加载成功: {model_path}")
        print(f"模型参数数量: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"设备: {self.device}")
    
    def beam_search_translate(self, src_text, beam_size=3, length_penalty=0.6, repetition_penalty=1.2):
        """
        使用beam search进行翻译
        :param src_text: 德语输入文本
        :param beam_size: beam大小
        :param length_penalty: 长度惩罚
        :param repetition_penalty: 重复惩罚
        :return: (翻译结果, 推理时间)
        """
        start_time = time.time()
        
        # 预处理输入
        de_tokens = self.dataset.de_tokenizer(src_text)
        src_tokens = [self.bos_token] + self.dataset.de_vocab[de_tokens] + [self.eos_token]
        src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
        
        # 初始化beam
        beams = [(torch.tensor([[self.bos_token]]).to(self.device), 0.0)]  # (sequence, score)
        
        with torch.no_grad():
            for step in range(self.max_seq_length):
                new_beams = []
                
                for seq, score in beams:
                    if seq[0, -1].item() == self.eos_token:
                        new_beams.append((seq, score))
                        continue
                    
                    # 模型前向传播
                    output = self.model(src_tensor, seq)
                    if isinstance(output, tuple):
                        out, logits = output
                        predict = logits[:, -1]
                    else:
                        out = output
                        predict = self.model.predictor(out[:, -1])
                    
                    # 应用重复惩罚
                    predict = self.apply_repetition_penalty(predict, seq, repetition_penalty)
                    
                    # 获取概率
                    log_probs = F.log_softmax(predict, dim=-1)
                    
                    # 获取top-k候选
                    top_log_probs, top_indices = torch.topk(log_probs, beam_size)
                    
                    for i in range(beam_size):
                        token_id = top_indices[0, i].item()
                        token_score = top_log_probs[0, i].item()
                        
                        new_seq = torch.cat([seq, torch.tensor([[token_id]]).to(self.device)], dim=1)
                        new_score = score + token_score
                        
                        new_beams.append((new_seq, new_score))
                
                # 选择最佳beam
                new_beams.sort(key=lambda x: self.calculate_score(x[1], x[0].size(1), length_penalty), reverse=True)
                beams = new_beams[:beam_size]
                
                # 检查是否所有beam都结束
                if all(seq[0, -1].item() == self.eos_token for seq, _ in beams):
                    break
        
        end_time = time.time()
        
        # 选择最佳结果
        best_seq, best_score = max(beams, key=lambda x: self.calculate_score(x[1], x[0].size(1), length_penalty))
        
        # 转换为文本
        tgt_tokens = best_seq.squeeze().tolist()
        translated = ' '.join(self.dataset.zh_vocab.lookup_tokens(tgt_tokens))
        translated = translated.replace("<s>", "").replace("</s>", "").strip()
        
        return translated, end_time - start_time
    
    def apply_repetition_penalty(self, logits, sequence, penalty=1.2):
        """应用重复惩罚"""
        if penalty == 1.0:
            return logits
        
        # 获取已生成的token
        generated_tokens = sequence.squeeze().tolist()
        
        # 对已出现的token应用惩罚
        for token in set(generated_tokens):
            if token < logits.size(-1):
                if logits[0, token] > 0:
                    logits[0, token] /= penalty
                else:
                    logits[0, token] *= penalty
        
        return logits
    
    def calculate_score(self, log_prob_sum, length, length_penalty):
        """计算beam search分数"""
        return log_prob_sum / (length ** length_penalty)
    
    def greedy_translate(self, src_text):
        """贪婪搜索翻译（用于对比）"""
        start_time = time.time()
        
        # 预处理输入
        de_tokens = self.dataset.de_tokenizer(src_text)
        src_tokens = [self.bos_token] + self.dataset.de_vocab[de_tokens] + [self.eos_token]
        src_tensor = torch.tensor(src_tokens).unsqueeze(0).to(self.device)
        
        # 初始化目标序列
        tgt_tensor = torch.tensor([[self.bos_token]]).to(self.device)
        
        with torch.no_grad():
            for _ in range(self.max_seq_length):
                output = self.model(src_tensor, tgt_tensor)
                if isinstance(output, tuple):
                    out, logits = output
                    predict = logits[:, -1]
                else:
                    out = output
                    predict = self.model.predictor(out[:, -1])
                
                next_token = torch.argmax(predict, dim=1)
                tgt_tensor = torch.cat([tgt_tensor, next_token.unsqueeze(0)], dim=1)
                
                if next_token.item() == self.eos_token:
                    break
        
        end_time = time.time()
        
        # 转换为文本
        tgt_tokens = tgt_tensor.squeeze().tolist()
        translated = ' '.join(self.dataset.zh_vocab.lookup_tokens(tgt_tokens))
        translated = translated.replace("<s>", "").replace("</s>", "").strip()
        
        return translated, end_time - start_time
    
    def compare_methods(self, src_text):
        """比较不同翻译方法"""
        print(f"原文: {src_text}")
        print("-" * 60)
        
        # 贪婪搜索
        greedy_result, greedy_time = self.greedy_translate(src_text)
        print(f"贪婪搜索: {greedy_result}")
        print(f"时间: {greedy_time:.4f}s")
        print()
        
        # Beam search
        beam_result, beam_time = self.beam_search_translate(src_text)
        print(f"Beam搜索: {beam_result}")
        print(f"时间: {beam_time:.4f}s")
        print()
        
        # 分析差异
        if greedy_result != beam_result:
            print("✓ Beam search产生了不同的结果")
        else:
            print("⚠ 两种方法产生相同结果")
        
        print(f"速度差异: {beam_time/greedy_time:.2f}x")


def test_improvements():
    """测试改进效果"""
    print("测试改进的翻译方法")
    print("=" * 60)
    
    # 使用学生模型进行测试
    model_path = "train_process/distillation/checkpoints/student_best.pt"
    data_path = "data/de-zh.txt/dezh_cleaned.txt"
    
    if not Path(model_path).exists():
        print(f"模型文件不存在: {model_path}")
        return
    
    translator = BeamSearchTranslator(model_path, data_path)
    
    # 测试句子
    test_sentences = [
        "Guten Morgen",
        "Ich liebe dich",
        "Wie geht es dir?",
        "Das Wetter ist schön",
        "Am Anfang schuf Gott Himmel und Erde"
    ]
    
    for sentence in test_sentences:
        print(f"\n{'='*60}")
        translator.compare_methods(sentence)


def main():
    parser = argparse.ArgumentParser(description='改进的德语-中文翻译')
    parser.add_argument('--model', default='train_process/distillation/checkpoints/student_best.pt', help='模型路径')
    parser.add_argument('--data_path', default='data/de-zh.txt/dezh_cleaned.txt', help='数据集路径')
    parser.add_argument('--text', help='要翻译的德语文本')
    parser.add_argument('--beam_size', type=int, default=3, help='Beam大小')
    parser.add_argument('--test', action='store_true', help='运行测试')
    parser.add_argument('--device', default='auto', help='设备')
    
    args = parser.parse_args()
    
    if args.test:
        test_improvements()
        return
    
    if not Path(args.model).exists():
        print(f"模型文件不存在: {args.model}")
        return
    
    translator = BeamSearchTranslator(args.model, args.data_path, args.device)
    
    if args.text:
        # 单次翻译
        result, time_taken = translator.beam_search_translate(args.text, beam_size=args.beam_size)
        print(f"原文: {args.text}")
        print(f"译文: {result}")
        print(f"时间: {time_taken:.4f}s")
    else:
        # 交互式翻译
        print("改进的德语-中文翻译器 (输入 'quit' 退出)")
        print("使用Beam Search和重复惩罚机制")
        
        while True:
            text = input("\n请输入德语文本: ").strip()
            if text.lower() in ['quit', 'exit', 'q']:
                break
            if text:
                result, time_taken = translator.beam_search_translate(text, beam_size=args.beam_size)
                print(f"译文: {result}")
                print(f"时间: {time_taken:.4f}s")


if __name__ == "__main__":
    main()
